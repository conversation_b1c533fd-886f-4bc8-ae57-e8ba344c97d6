// 订单列表页面
// 修复问题：
// 1. 将 api.getOrderList 改为 api.getMyOrders（修复 API 方法名错误）
// 2. 将分页参数 page/limit 改为 pageNum/pageSize（匹配后端期望的参数名）
// 3. 添加 role 参数区分买家/卖家视角
// 4. 优化分页重置逻辑，统一使用 refreshOrderList 方法

const app = getApp()
const api = require('../../config/api.js')
const systemInfoService = require('../../services/systemInfo.js')
const util = require('../../utils/util.js')

Page({
  data: {
    activeTab: 0,
    orderList: [],
    loading: false,
    page: 1,
    limit: 10,
    hasMore: true,
    // 文件服务器URL
    fileUrl: '',
    // 订单状态映射（与后端 OrderStatus 枚举对应）
    statusMap: {
      '0': '全部',
      '1': '待联系', 
      '2': '待确认', 
      '3': '已完成', 
      '4': '已取消', 
      '8': '终止订单', 
    },
    // 角色类型
    roleType: 'buyer', // buyer-买家, seller-卖家
    // 订单状态筛选
    statusFilter: null,
    // 确认弹框相关
    showConfirmDialog: false,
    confirmText: '',
    currentAction: null,
    currentOrderId: null
  },

  onLoad: function(options) {
    // 如果有传入的tab参数，切换到对应tab
    if (options.tab) {
      this.setData({
        activeTab: parseInt(options.tab) || 0
      })
    }

    // 如果有传入的role参数，切换到对应角色
    if (options.role) {
      this.setData({
        roleType: options.role
      })

      // 根据角色类型设置导航栏标题
      wx.setNavigationBarTitle({
        title: options.role === 'buyer' ? '我买入的' : '我卖出的'
      })
    } else {
      // 默认设置导航栏标题
      wx.setNavigationBarTitle({
        title: this.data.roleType === 'buyer' ? '我买入的' : '我卖出的'
      })
    }

    // 获取系统信息中的文件服务器URL
    const systemInfo = wx.getStorageSync('systemInfo') || {}
    this.setData({
      fileUrl: systemInfo.fileUrl || ''
    })

    // 加载订单列表
    this.loadOrderList()
  },

  // 切换标签页
  onTabChange: function(event) {
    const tabIndex = event.detail.index

    // 根据标签页设置状态筛选
    let statusFilter = null
    switch (tabIndex) {
      case 0: // 全部
        statusFilter = null
        break
      case 1: // 待双方确认
        statusFilter = '1'
        break
      case 2: //  待联系卖家       statusFilter = '2'
        break
      case 3: // 已完成
        statusFilter = '3' // 已完成
        break
      case 4: // 取消
        statusFilter = '4' // 已取消
        break
      case 5: // 已终止
        statusFilter = '8' // 已终止
        break
    }

    this.setData({
      activeTab: tabIndex,
      statusFilter: statusFilter,
      orderList: [],
      page: 1,
      hasMore: true
    })

    // 重新加载订单列表
    this.loadOrderList()
  },

  // 刷新订单列表（重置分页状态）
  refreshOrderList: function() {
    this.setData({
      orderList: [],
      page: 1,
      hasMore: true
    })
    this.loadOrderList()
  },

  async loadOrderList() {
    if (this.data.loading || !this.data.hasMore) {
      return
    }

    this.setData({ loading: true })

    try {
      // 构建请求参数
      const params = {
        pageNum: this.data.page,
        pageSize: this.data.limit,
        role: this.data.roleType === 'buyer' ? 'buyer' : 'seller'
      }

      // 只有当状态不为空时才添加 status 参数
      const status = this.getStatusByTab()
      if (status !== null) {
        params.status = status
      }

      const res = await api.getMyOrders(params)

      if (res.code === 200) {
        let orderList = res.rows || []
        const total = res.total || 0;

        // 使用系统信息服务处理图片
        orderList = await Promise.all(orderList.map(async item => {
          // 处理商品图片
          if (item.productImages && typeof item.productImages === 'string') {
            const imageArray = item.productImages.split(',')
            if (imageArray.length > 0 && imageArray[0].trim()) {
              item.productImageUrl = await systemInfoService.processImageUrl(imageArray[0].trim())
            }
          }
          return item
        }))

        // 判断是否是首页加载还是加载更多
        if (this.data.page === 1) {
          this.setData({
            orderList: orderList
          })
        } else {
          this.setData({
            orderList: this.data.orderList.concat(orderList)
          })
        }

        // 正确判断是否还有更多数据：基于总数和当前已加载的数据量
        const currentTotal = this.data.page === 1 ? 0 : this.data.orderList.length - orderList.length;
        const hasMore = currentTotal + orderList.length < total;

        this.setData({
          hasMore: hasMore,
          page: this.data.page + 1
        })
      }
    } catch (error) {
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
      wx.stopPullDownRefresh()
      this.setData({ loading: false })
    }
  },

  // 查看订单详情
  onOrderDetail: function(event) {
    const orderId = event.currentTarget.dataset.id

    wx.navigateTo({
      url: `/pages/order/detail?id=${orderId}`
    })
  },

  // 确认收货
  async confirmReceive(orderId) {
    try {
      wx.showLoading({ title: '处理中...' })
      const res = await api.confirmReceive(orderId)
      
      if (res.code === 200) {
        wx.showToast({ title: '确认收货成功', icon: 'success' })
        this.loadOrderList(true)
      } else {
        throw new Error(res.msg || '确认收货失败')
      }
    } catch (err) {
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },



  // 申请退款
  onApplyRefund: function(event) {
    const orderId = event.currentTarget.dataset.id

    // 跳转到订单详情页面，让用户在详情页面申请退款
    wx.navigateTo({
      url: `/pages/order/detail?id=${orderId}&action=refund`
    })
  },

  // 确认联系按钮点击
  onContactOrder: function(event) {
    const orderId = event.currentTarget.dataset.id
    this.showConfirmDialog('确认已经联系卖家了吗？', 'contactOrder', orderId)
  },

  // 取消订单按钮点击
  onCancelOrder: function(event) {
    const orderId = event.currentTarget.dataset.id
    this.showConfirmDialog('确定要取消该订单吗？', 'cancelOrder', orderId)
  },

  // 完成订单按钮点击
  onFinishOrder: function(event) {
    const orderId = event.currentTarget.dataset.id
    this.showConfirmDialog('确认已经完成交易了吗？', 'finishOrder', orderId)
  },

  // 终止订单按钮点击
  onTerminateOrder: function(event) {
    const orderId = event.currentTarget.dataset.id
    this.showConfirmDialog('确定要终止该订单吗？终止后订单将无法继续进行。', 'terminateOrder', orderId)
  },

  // 显示确认弹框
  showConfirmDialog: function(text, action, orderId) {
    this.setData({
      showConfirmDialog: true,
      confirmText: text,
      currentAction: action,
      currentOrderId: orderId
    })
  },

  // 确认操作
  confirmAction: function() {
    this.setData({ showConfirmDialog: false })

    const { currentAction, currentOrderId } = this.data

    switch (currentAction) {
      case 'contactOrder':
        this.executeContactOrder(currentOrderId)
        break
      case 'cancelOrder':
        this.executeCancelOrder(currentOrderId)
        break
      case 'finishOrder':
        this.executeFinishOrder(currentOrderId)
        break
      case 'terminateOrder':
        this.executeTerminateOrder(currentOrderId)
        break
    }
  },

  // 取消操作
  cancelAction: function() {
    this.setData({
      showConfirmDialog: false,
      currentAction: null,
      currentOrderId: null
    })
  },

  // 执行确认联系
  async executeContactOrder(orderId) {
    try {
      wx.showLoading({ title: '处理中...' })
      const res = await api.contactOrder(orderId)

      if (res.code === 200) {
        wx.showToast({ title: '确认联系成功', icon: 'success' })
        this.refreshOrderList()
      } else {
        throw new Error(res.msg || '确认联系失败')
      }
    } catch (err) {
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 执行取消订单
  async executeCancelOrder(orderId) {
    try {
      wx.showLoading({ title: '处理中...' })
      const res = await api.cancelOrder(orderId)

      if (res.code === 200) {
        wx.showToast({ title: '取消订单成功', icon: 'success' })
        this.refreshOrderList()
      } else {
        throw new Error(res.msg || '取消订单失败')
      }
    } catch (err) {
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 执行完成订单
  async executeFinishOrder(orderId) {
    try {
      wx.showLoading({ title: '处理中...' })
      const res = await api.confirmOrder(orderId)

      if (res.code === 200) {
        wx.showToast({ title: '完成订单成功', icon: 'success' })
        this.refreshOrderList()
      } else {
        throw new Error(res.msg || '完成订单失败')
      }
    } catch (err) {
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 执行终止订单
  async executeTerminateOrder(orderId) {
    try {
      wx.showLoading({ title: '处理中...' })
      const res = await api.terminationOrder(orderId)

      if (res.code === 200) {
        wx.showToast({ title: '终止订单成功', icon: 'success' })
        this.refreshOrderList()
      } else {
        throw new Error(res.msg || '终止订单失败')
      }
    } catch (err) {
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 同意退款
  async agreeRefund(orderId) {
    try {
      wx.showLoading({ title: '处理中...' })
      const res = await api.agreeRefund(orderId)
      
      if (res.code === 200) {
        wx.showToast({ title: '同意退款成功', icon: 'success' })
        this.loadOrderList(true)
      } else {
        throw new Error(res.msg || '同意退款失败')
      }
    } catch (err) {
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 拒绝退款
  async rejectRefund(orderId, reason) {
    try {
      wx.showLoading({ title: '处理中...' })
      const res = await api.rejectRefund(orderId, reason)
      
      if (res.code === 200) {
        wx.showToast({ title: '拒绝退款成功', icon: 'success' })
        this.loadOrderList(true)
      } else {
        throw new Error(res.msg || '拒绝退款失败')
      }
    } catch (err) {
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshOrderList()
    wx.stopPullDownRefresh()
  },

  // 触底加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrderList()
    }
  },

  getStatusByTab() {
    switch (this.data.activeTab) {
      case 0:
        return null
      case 1:
        return '1'
      case 2:
        return '2'
      case 3:
        return '3'
      case 4:
        return '4'
      case 5:
        return '8'
      default:
        return null
    }
  }
})
