package com.fs.swap.wx.controller;

import com.fs.swap.common.utils.ip.IpUtils;
import com.fs.swap.system.domain.UserLoginRecord;
import com.fs.swap.system.service.IUserLoginRecordService;
import com.fs.swap.wx.pojo.dto.WxLoginInfoDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * UserController 测试类
 * 主要测试异步登录记录功能中的IP获取优化
 */
@ExtendWith(MockitoExtension.class)
public class UserControllerTest {

    @Mock
    private IUserLoginRecordService userLoginRecordService;

    @Mock
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @InjectMocks
    private UserController userController;

    /**
     * 测试异步登录记录中IP地址的正确获取
     * 验证IP地址在主线程中获取，而不是在异步线程中获取
     */
    @Test
    public void testLoginRecordWithCorrectIpHandling() throws Exception {
        // 准备测试数据
        long userId = 123L;
        WxLoginInfoDTO loginInfo = new WxLoginInfoDTO();
        loginInfo.setPlatform("微信小程序");
        loginInfo.setBrand("iPhone");
        loginInfo.setModel("iPhone 12");
        loginInfo.setVersion("8.0.0");
        loginInfo.setSdkVersion("2.19.4");
        loginInfo.setSystem("iOS 15.0");
        boolean isFirstLogin = true;
        String expectedIp = "*************";

        // 模拟真实的异步执行器行为
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            // 在新线程中执行任务，模拟真实的异步环境
            CompletableFuture.runAsync(task);
            return null;
        }).when(threadPoolTaskExecutor).execute(any(Runnable.class));

        // 使用静态模拟来模拟IpUtils.getIpAddr()的行为
        try (MockedStatic<IpUtils> mockedIpUtils = mockStatic(IpUtils.class)) {
            // 模拟在主线程中能够正确获取IP地址
            mockedIpUtils.when(IpUtils::getIpAddr).thenReturn(expectedIp);

            // 通过反射调用私有方法来测试（实际项目中可能需要调整访问修饰符）
            // 这里我们直接测试loginRecord方法
            java.lang.reflect.Method loginRecordMethod = UserController.class
                    .getDeclaredMethod("loginRecord", long.class, WxLoginInfoDTO.class, boolean.class, String.class);
            loginRecordMethod.setAccessible(true);

            // 执行测试
            loginRecordMethod.invoke(userController, userId, loginInfo, isFirstLogin, expectedIp);

            // 验证userLoginRecordService.insertUserLoginRecord被调用
            verify(userLoginRecordService, timeout(1000)).insertUserLoginRecord(any(UserLoginRecord.class));

            // 验证传入的UserLoginRecord包含正确的IP地址
            verify(userLoginRecordService).insertUserLoginRecord(argThat(record -> 
                expectedIp.equals(record.getIp()) && 
                record.getUserId() == userId &&
                record.getPlatform().equals(loginInfo.getPlatform()) &&
                record.getBrand().equals(loginInfo.getBrand()) &&
                record.getModel().equals(loginInfo.getModel()) &&
                record.getVersion().equals(loginInfo.getVersion()) &&
                record.getSdkVersion().equals(loginInfo.getSdkVersion()) &&
                record.getSystem().equals(loginInfo.getSystem()) &&
                record.getIsFirstLogin() == (isFirstLogin ? 1 : 0)
            ));
        }
    }

    /**
     * 测试在异步环境中不会调用IpUtils.getIpAddr()
     * 这个测试确保我们的优化生效了
     */
    @Test
    public void testAsyncTaskDoesNotCallIpUtils() throws Exception {
        // 准备测试数据
        long userId = 456L;
        WxLoginInfoDTO loginInfo = new WxLoginInfoDTO();
        loginInfo.setPlatform("微信小程序");
        boolean isFirstLogin = false;
        String preObtainedIp = "********";

        // 模拟异步执行
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            CompletableFuture.runAsync(task);
            return null;
        }).when(threadPoolTaskExecutor).execute(any(Runnable.class));

        try (MockedStatic<IpUtils> mockedIpUtils = mockStatic(IpUtils.class)) {
            // 通过反射调用loginRecord方法
            java.lang.reflect.Method loginRecordMethod = UserController.class
                    .getDeclaredMethod("loginRecord", long.class, WxLoginInfoDTO.class, boolean.class, String.class);
            loginRecordMethod.setAccessible(true);

            // 执行测试 - 直接传入IP地址，不应该调用IpUtils.getIpAddr()
            loginRecordMethod.invoke(userController, userId, loginInfo, isFirstLogin, preObtainedIp);

            // 等待异步任务完成
            Thread.sleep(100);

            // 验证IpUtils.getIpAddr()没有被调用（因为我们传入了IP地址）
            mockedIpUtils.verify(IpUtils::getIpAddr, never());

            // 验证记录被正确插入
            verify(userLoginRecordService, timeout(1000)).insertUserLoginRecord(any(UserLoginRecord.class));
        }
    }
}
